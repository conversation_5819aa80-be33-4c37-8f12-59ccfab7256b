using System.Net;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Orion.SharedKernel.Api.Controllers;
using Orion.SharedKernel.Domain.Entities.Error;
using Reports.Application.DTOs.Reports;
using Reports.Application.DTOs.Reports.MassBalance;
using Reports.Application.Features.Web.Queries.Reports.ExportCompressedReports;
using Reports.Application.Features.Web.Queries.Reports.ExportDiscounts;
using Reports.Application.Features.Web.Queries.Reports.ExportReport;
using Reports.Application.Features.Web.Queries.Reports.GetReportPreview;
using Reports.Application.Features.Web.Queries.Reports.MassBalance;

namespace Reports.Api.Controllers.v1.Web;

[ApiVersion("1.0"), Authorize]
public class ReportsController : OrionController
{
    [EndpointSummary("Obtiene la vista previa de un reporte")]
    [EndpointDescription("Obtiene la vista previa de un reporte basado en los filtros proporcionados.")]
    [ProducesResponseType<List<F14PreviewResponseDto>>((int)HttpStatusCode.OK)]
    [ProducesResponseType<List<F34PreviewResponseDto>>((int)HttpStatusCode.OK)]
    [HttpGet("Preview")]
    public async Task<IActionResult> GetReportPreview([FromQuery] GetReportPreviewRequest request)
    {
        var response = await Mediator.Send(request);

        return Ok(response.ReportPreview);
    }

    [EndpointSummary("Obtiene el reporte de balance de masas")]
    [EndpointDescription("Obtiene el objeto representativo de un reporte de balance de masas de un periodo determinado.")]
    [ProducesResponseType<MassBalanceResponseDto>((int)HttpStatusCode.OK)]
    [ProducesResponseType<Error>((int)HttpStatusCode.Conflict)]
    [ProducesResponseType((int)HttpStatusCode.Unauthorized)]
    [HttpGet("MassBalance")]
    public async Task<IActionResult> GetMassBalance([FromQuery] GetMassBalanceRequest request)
    {
        var response = await Mediator.Send(request);

        return Ok(response.MassBalance);
    }

    [EndpointSummary("Exporta los reportes 14 y 34")]
    [EndpointDescription("Exporta los reportes regulatorios 14 y 34 en el formato especificado.")]
    [ProducesResponseType<FileResult>((int)HttpStatusCode.OK)]
    [ProducesResponseType<Error>((int)HttpStatusCode.InternalServerError)]
    [HttpGet("Export")]
    public async Task ExportReport([FromQuery] ReportExportRequest request)
    {
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }
    
    [EndpointSummary("Genera un ZIP con reportes comprimidos")]
    [EndpointDescription("Genera un ZIP con los reportes formateados según la regulación de la intendencia de un periodo seleccionado en formato CSV.")]
    [ProducesResponseType<FileResult>((int)HttpStatusCode.OK)]
    [ProducesResponseType<Error>((int)HttpStatusCode.InternalServerError)]
    [HttpGet("ExportCompressedReports")]
    public async Task ExportCompressedReports([FromQuery] CompressedReportsRequestDto requestDto)
    {
        var request = new ExportCompressedReportsRequest(requestDto.FromDate, requestDto.ToDate);
        
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }
    
    [EndpointSummary("Exporta reporte de descuentos")]
    [EndpointDescription("Exporta un excel o csv con un listado de descuentos de toneladas por numero de tiquete para un periodo determinado.")]
    [ProducesResponseType<FileResult>((int)HttpStatusCode.OK)]
    [ProducesResponseType<Error>((int)HttpStatusCode.InternalServerError)]
    [HttpGet("DiscountsReport")]
    public async Task ExportDiscounts([FromQuery] ExportDiscountsRequest request)
    {
        var response = await Mediator.Send(request);

        await response.Result.ExecuteAsync(HttpContext);
    }
}