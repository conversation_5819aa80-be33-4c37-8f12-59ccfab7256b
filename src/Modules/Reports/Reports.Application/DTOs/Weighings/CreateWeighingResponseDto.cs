using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Weighings;

public record CreateWeighingResponseDto(
    [property: Required]
    [property: Description("Lista de pesajes insertados, modificados o anulados")]
    [property: JsonPropertyName("Pesajes")]
    List<CreateWeighingItemResponseDto> Items,

    [property: Required]
    [property: Range(0, int.MaxValue)]
    [property: Description("Total de pesajes manipulados")]
    [property: JsonPropertyName("Total")]
    int Total
);