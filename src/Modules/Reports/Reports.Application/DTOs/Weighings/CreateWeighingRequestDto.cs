using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Weighings;

public record CreateWeighingRequestDto(
    [property: MaxLength(10)]
    [property: Description("Nro. de comprobante. 10 carácteres alfanuméricos")]
    [property: JsonPropertyName("NroComprobante")]
    string? Id,

    [property: MinLength(5)]
    [property: Description("Placa del vehículo. Mínimo 5 carácteres alfanuméricos")]
    [property: JsonPropertyName("Placa")]
    string? LicensePlate,

    [property: Description("Fecha de ingreso. Formato: YYYY-MM-DD HH:MM:SS")]
    [property: JsonPropertyName("FechaIngreso")]
    string? EntryDate,

    [property: Description("Hora de ingreso. Formato: YYYY-MM-DD HH:MM:SS")]
    [property: JsonPropertyName("HoraIngreso")]
    string? EntryTime,

    [property: MaxLength(5)]
    [property: Description("Codigo del municipio. Deben ser 5 caracteres, si no, se completara con ceros a la izquierda")]
    [property: JsonPropertyName("MunicipioViaje")]
    string? TownCode,

    [property: Description("Número de identificación tributaria del transporte")]
    [property: JsonPropertyName("NitViaje")]
    string? NIT,

    [property: Range(0, double.MaxValue)]
    [property: Description("Peso de entrada")]
    [property: JsonPropertyName("PesoEntrada")]
    double ArrivingWeight,

    [property: Range(0, double.MaxValue)]
    [property: Description("Peso de salida")]
    [property: JsonPropertyName("PesoDeposito")]
    double LeavingWeight,

    [property: Description("Fecha de salida. Formato: YYYY-MM-DD HH:MM:SS")]
    [property: JsonPropertyName("FechaSalida")]
    string? EgressDate,

    [property: Description("Hora de salida. Formato: YYYY-MM-DD HH:MM:SS")]
    [property: JsonPropertyName("HoraSalida")]
    string? EgressTime,

    [property: Description("Código tipo de material")]
    [property: JsonPropertyName("TipoResiduo")]
    string? MaterialType,

    [property: Description("Tipo de origen. (1) NUAP, (2) NUET, (3) NUECA, (4) Otros")]
    [property: JsonPropertyName("TipoOrigen")]
    string? OriginType,

    [property: Description("Código de área de prestación del servicio")]
    [property: JsonPropertyName("Nuap")]
    string? NUAP,

    [property: Description("Tipo de carga. (1) A, (2) M")]
    [property: JsonPropertyName("Tipo")]
    string? LoadingType,

    [property: Description("Fecha y hora de carga del registro en la balanza. Formato: YYYY-MM-DD HH:MM:SS")]
    [property: JsonPropertyName("FechaCargue")]
    string? LoadingDate,

    [property: Description("Fecha y hora de cancelación. Formato: YYYY-MM-DD HH:MM:SS")]
    [property: JsonPropertyName("FechaCancela")]
    string? CancelDate
);