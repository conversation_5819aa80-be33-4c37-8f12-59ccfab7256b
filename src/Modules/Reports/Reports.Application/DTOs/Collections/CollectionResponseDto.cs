using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Collections;

public record CollectionResponseDto(
    [property: Required]
    [property: Description("Identificador de la recolección vehicular registrada. Formato: NUAP-Número de Matrícula-Fecha de Recolección")]
    [property: JsonPropertyName("IdRecoleccionVehicular")]
    string VehicleRetrievalId,

    [property: Required]
    [property: Description("Identificador de la recolección por micro ruta registrada. Formato: NUAP-Número de Matrícula-Fecha de Recolección")]
    [property: JsonPropertyName("IdRecoleccionMicroRuta")]
    string MicroRouteCollectionId
);