using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Reports.Application.DTOs.Collections;

public record CollectionRequestDto(
    [property: Required]
    [property: Description("Código del lugar de depósito")]
    [property: JsonPropertyName("Nuap")]
    string NUAP,

    [property: Required]
    [property: MinLength(5)]
    [property: Description("Placa del vehículo. Mínimo 5 carácteres alfanuméricos")]
    [property: JsonPropertyName("Placa")]
    string LicensePlate,

    [property: Required]
    [property: Description("Fecha de carga del registro en la balanza. Formato: YYYY-MM-DD HH:MM:SS")]
    [property: JsonPropertyName("FechaYHoraRecoleccion")]
    string CollectionDate,

    [property: Required]
    [property: Description("Tipo de origen. (1) NUAP, (2) NUET, (3) NUECA, (4) Otros")]
    [property: JsonPropertyName("TipoOrigen")]
    string OriginType,

    [property: Required]
    [property: Description("Código de micro ruta")]
    [property: JsonPropertyName("NroMicroruta")]
    string Microroute,


    [property: Range(0, double.MaxValue)]
    [property: Description("Cantidad en toneladas de residuos sólidos urbanos")]
    [property: JsonPropertyName("TonLimUrb")]
    double UrbanCleaningTons,

    [property: Range(0, double.MaxValue)]
    [property: Description("Cantidad en toneladas de barrido")]
    [property: JsonPropertyName("TonBarrido")]
    double SweepingTons,

    [property: Range(0, double.MaxValue)]
    [property: Description("Cantidad en toneladas de residuos rechazados")]
    [property: JsonPropertyName("TonRechazos")]
    double RejectedTons,

    [property: Range(0, double.MaxValue)]
    [property: Description("Cantidad de residuos aprovechables")]
    [property: JsonPropertyName("TonResAprob")]
    double RecyclableTons,

    [property: Range(0, double.MaxValue)]
    [property: Description("Cantidad de toneladas")]
    [property: JsonPropertyName("Toneladas")]
    double Tons,

    [property: Range(0, double.MaxValue)]
    [property: Description("Coste de peajes")]
    [property: JsonPropertyName("ValorPeaje")]
    double Toll,

    [property: Required]
    [property: Description("Número de identificación tributaria")]
    [property: JsonPropertyName("NitViaje")]
    string NIT,

    [property: Description("Nombre de la empresa")]
    [property: JsonPropertyName("Empresa")]
    string? CompanyName,

    [property: Description("Código del municipio de donde provienen los residuos")]
    [property: JsonPropertyName("CodDANE_Empresa")]
    string? DaneCode
);
